<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        label {
            font-weight: bold;
            color: #333;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            padding: 10px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #005a87;
        }
        .status {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .title {
            text-align: center;
            color: #007cba;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h3 class="title">华宝证券自动登录</h3>
        
        <div class="input-group">
            <label for="username">用户名/手机号:</label>
            <input type="text" id="username" placeholder="请输入用户名或手机号">
        </div>
        
        <div class="input-group">
            <label for="password">密码:</label>
            <input type="password" id="password" placeholder="请输入密码">
        </div>
        
        <div class="checkbox-group">
            <input type="checkbox" id="autoLogin">
            <label for="autoLogin">自动登录</label>
        </div>
        
        <button id="saveBtn">保存设置</button>
        <button id="loginBtn">立即登录</button>
        <button id="clearBtn">清除数据</button>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
