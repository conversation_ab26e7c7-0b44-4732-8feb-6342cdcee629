# 华宝证券自动登录插件

这是一个Chrome浏览器插件，可以自动登录华宝证券交易平台。

## 功能特性

- 🔐 自动填充用户名和密码
- 🚀 一键登录功能
- 💾 安全存储登录凭据
- 🔄 自动登录选项
- 🎯 智能识别登录表单元素

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本插件的文件夹
6. 插件安装完成

### 方法二：打包安装

1. 在Chrome扩展程序页面点击"打包扩展程序"
2. 选择插件文件夹，生成.crx文件
3. 将.crx文件拖拽到Chrome扩展程序页面安装

## 使用方法

### 首次设置

1. 点击浏览器工具栏中的插件图标
2. 在弹出窗口中输入你的华宝证券账号信息：
   - 用户名/手机号
   - 密码
3. 选择是否启用"自动登录"
4. 点击"保存设置"

### 登录方式

#### 方式一：自动登录
- 启用"自动登录"选项后，访问华宝证券登录页面时会自动填充并提交登录信息

#### 方式二：手动触发
- 在华宝证券登录页面点击插件图标
- 点击"立即登录"按钮

#### 方式三：快速跳转
- 在任意页面点击插件图标
- 点击"立即登录"会自动打开登录页面并执行登录

## 安全说明

- 登录凭据使用Chrome的同步存储API安全保存
- 数据仅存储在本地，不会上传到任何服务器
- 可随时通过"清除数据"按钮删除保存的信息
- 建议定期更改密码以确保账户安全

## 支持的页面

- 华宝证券手机版登录页面：`https://m.touker.com/account/login/*`
- 自动识别多种登录表单格式

## 故障排除

### 登录失败
1. 检查用户名和密码是否正确
2. 确认网络连接正常
3. 检查华宝证券网站是否正常访问
4. 如果有验证码，需要手动输入

### 插件无法工作
1. 确认插件已正确安装并启用
2. 刷新华宝证券登录页面
3. 检查浏览器控制台是否有错误信息
4. 尝试重新安装插件

### 自动登录不生效
1. 确认已启用"自动登录"选项
2. 确认已保存正确的登录信息
3. 清除浏览器缓存后重试

## 技术实现

- **Manifest V3**：使用最新的Chrome扩展API
- **Content Scripts**：在登录页面注入自动登录脚本
- **Background Service Worker**：处理后台逻辑
- **Storage API**：安全存储用户配置
- **Scripting API**：动态执行登录脚本

## 文件结构

```
华宝自动挂单/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹出窗口界面
├── popup.js               # 弹出窗口逻辑
├── content.js             # 内容脚本（页面注入）
├── background.js          # 后台服务脚本
├── icons/                 # 插件图标文件夹
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

## 更新日志

### v1.0 (2025-07-30)
- 初始版本发布
- 支持自动登录华宝证券
- 提供用户友好的设置界面
- 实现安全的凭据存储

## 免责声明

本插件仅供学习和个人使用，使用者需要：
- 确保遵守华宝证券的使用条款
- 自行承担使用风险
- 保护好个人账户信息安全

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件反馈

---

**注意**：请确保你的华宝证券账户安全，不要在公共计算机上使用此插件。
