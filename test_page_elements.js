// 测试脚本 - 在华宝证券登录页面的控制台中运行此脚本
// 用于检测页面元素是否能被插件正确识别

console.log('🔍 开始检测华宝证券登录页面元素...');

// 用户名输入框选择器
const usernameSelectors = [
    'input[placeholder*="账号"]',
    'input[placeholder*="用户名"]',
    'input[placeholder*="手机"]',
    'input[type="text"]:not([placeholder*="验证码"])',
    'input[name="username"]',
    'input[name="mobile"]',
    'input[name="phone"]',
    'input[type="text"]:first-of-type'
];

// 密码输入框选择器
const passwordSelectors = [
    'input[placeholder*="密码"]',
    'input[type="password"]',
    'input[name="password"]'
];

// 登录按钮选择器
const loginButtonSelectors = [
    'button:contains("登录")',
    'button:contains("登陆")',
    'input[value*="登录"]',
    'input[value*="登陆"]',
    'button[type="submit"]',
    'input[type="submit"]',
    '.login-btn',
    '.submit-btn',
    '[class*="login"]',
    '[class*="submit"]',
    'button',
    'input[type="button"]'
];

// 检测用户名输入框
console.log('\n📝 检测用户名输入框:');
let usernameInput = null;
for (const selector of usernameSelectors) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`✅ 找到用户名输入框: ${selector}`);
        console.log(`   元素数量: ${elements.length}`);
        elements.forEach((el, index) => {
            console.log(`   [${index}] placeholder: "${el.placeholder}", name: "${el.name}", type: "${el.type}"`);
        });
        if (!usernameInput) {
            usernameInput = elements[0];
        }
    }
}

if (!usernameInput) {
    console.log('❌ 未找到用户名输入框');
    console.log('🔍 页面中所有的input元素:');
    document.querySelectorAll('input').forEach((el, index) => {
        console.log(`   [${index}] type: "${el.type}", name: "${el.name}", placeholder: "${el.placeholder}", class: "${el.className}"`);
    });
}

// 检测密码输入框
console.log('\n🔒 检测密码输入框:');
let passwordInput = null;
for (const selector of passwordSelectors) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`✅ 找到密码输入框: ${selector}`);
        console.log(`   元素数量: ${elements.length}`);
        elements.forEach((el, index) => {
            console.log(`   [${index}] placeholder: "${el.placeholder}", name: "${el.name}", type: "${el.type}"`);
        });
        if (!passwordInput) {
            passwordInput = elements[0];
        }
    }
}

if (!passwordInput) {
    console.log('❌ 未找到密码输入框');
}

// 检测登录按钮
console.log('\n🔘 检测登录按钮:');
let loginButton = null;

// 特殊处理包含文本的选择器
const textSelectors = ['button:contains("登录")', 'button:contains("登陆")'];
for (const selector of textSelectors) {
    const buttons = document.querySelectorAll('button');
    for (const button of buttons) {
        if (button.textContent.includes('登录') || button.textContent.includes('登陆')) {
            console.log(`✅ 找到登录按钮(文本匹配): button`);
            console.log(`   文本内容: "${button.textContent.trim()}"`);
            console.log(`   类名: "${button.className}"`);
            if (!loginButton) {
                loginButton = button;
            }
        }
    }
}

// 检查其他选择器
const otherButtonSelectors = loginButtonSelectors.filter(s => !s.includes(':contains'));
for (const selector of otherButtonSelectors) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`✅ 找到按钮元素: ${selector}`);
        console.log(`   元素数量: ${elements.length}`);
        elements.forEach((el, index) => {
            console.log(`   [${index}] 文本: "${el.textContent || el.value}", type: "${el.type}", class: "${el.className}"`);
        });
    }
}

if (!loginButton) {
    console.log('❌ 未找到登录按钮');
    console.log('🔍 页面中所有的button和input[type="submit"]元素:');
    document.querySelectorAll('button, input[type="submit"], input[type="button"]').forEach((el, index) => {
        console.log(`   [${index}] 标签: ${el.tagName}, 文本: "${el.textContent || el.value}", type: "${el.type}", class: "${el.className}"`);
    });
}

// 总结
console.log('\n📊 检测结果总结:');
console.log(`用户名输入框: ${usernameInput ? '✅ 已找到' : '❌ 未找到'}`);
console.log(`密码输入框: ${passwordInput ? '✅ 已找到' : '❌ 未找到'}`);
console.log(`登录按钮: ${loginButton ? '✅ 已找到' : '❌ 未找到'}`);

// 如果都找到了，测试填充
if (usernameInput && passwordInput) {
    console.log('\n🧪 测试填充功能:');
    
    // 保存原始值
    const originalUsername = usernameInput.value;
    const originalPassword = passwordInput.value;
    
    // 测试填充
    usernameInput.value = 'test_username';
    usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    passwordInput.value = 'test_password';
    passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    console.log('✅ 测试填充完成');
    console.log('💡 如果看到输入框中有测试内容，说明填充功能正常');
    
    // 恢复原始值
    setTimeout(() => {
        usernameInput.value = originalUsername;
        passwordInput.value = originalPassword;
        usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
        passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
        console.log('🔄 已恢复原始值');
    }, 3000);
}

console.log('\n🎯 如果插件无法正常工作，请将以上信息反馈给开发者');
