// 华宝证券页面调试脚本 - 直接在页面上运行
// 在华宝证券登录页面按F12，在Console中一行一行运行以下命令

// 1. 查看所有输入框
console.log('=== 所有输入框 ===');
document.querySelectorAll('input').forEach((el, i) => {
    console.log(`${i}: type="${el.type}" placeholder="${el.placeholder}" name="${el.name}" id="${el.id}" class="${el.className}"`);
});

// 2. 查看所有按钮
console.log('=== 所有按钮 ===');
document.querySelectorAll('button').forEach((el, i) => {
    console.log(`${i}: text="${el.textContent.trim()}" class="${el.className}" type="${el.type}"`);
});

// 3. 查看所有复选框
console.log('=== 所有复选框 ===');
document.querySelectorAll('input[type="checkbox"]').forEach((el, i) => {
    const parentText = el.parentElement ? el.parentElement.textContent.trim() : '';
    console.log(`${i}: checked=${el.checked} text="${parentText}"`);
});

// 4. 尝试自动填充测试
console.log('=== 开始测试填充 ===');

// 查找第一个text输入框（通常是用户名）
const usernameInput = document.querySelector('input[type="text"]');
if (usernameInput) {
    console.log('找到用户名输入框，开始测试...');
    usernameInput.click();
    setTimeout(() => {
        usernameInput.focus();
        usernameInput.value = 'test123';
        usernameInput.dispatchEvent(new Event('input', {bubbles: true}));
        usernameInput.dispatchEvent(new Event('change', {bubbles: true}));
        console.log('用户名填充完成');
    }, 500);
} else {
    console.log('❌ 未找到用户名输入框');
}

// 查找密码输入框
const passwordInput = document.querySelector('input[type="password"]');
if (passwordInput) {
    console.log('找到密码输入框，开始测试...');
    setTimeout(() => {
        passwordInput.click();
        passwordInput.focus();
        passwordInput.value = 'test456';
        passwordInput.dispatchEvent(new Event('input', {bubbles: true}));
        passwordInput.dispatchEvent(new Event('change', {bubbles: true}));
        console.log('密码填充完成');
    }, 1000);
} else {
    console.log('❌ 未找到密码输入框');
}

// 查找并勾选复选框
const checkbox = document.querySelector('input[type="checkbox"]');
if (checkbox) {
    console.log('找到复选框，开始测试...');
    setTimeout(() => {
        if (!checkbox.checked) {
            checkbox.click();
            console.log('复选框已勾选');
        }
    }, 1500);
} else {
    console.log('❌ 未找到复选框');
}

console.log('=== 测试完成，请查看页面变化 ===');
