// 后台脚本
console.log('华宝证券自动登录插件后台脚本已启动');

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener(function(details) {
    if (details.reason === 'install') {
        console.log('插件首次安装');
        // 可以在这里设置默认配置
        chrome.storage.sync.set({
            autoLogin: false
        });
    } else if (details.reason === 'update') {
        console.log('插件已更新');
    }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
    // 当页面完成加载且是华宝证券登录页面时
    if (changeInfo.status === 'complete' && 
        tab.url && 
        tab.url.includes('m.touker.com/account/login')) {
        
        console.log('检测到华宝证券登录页面');
        
        // 检查是否启用自动登录
        chrome.storage.sync.get(['autoLogin'], function(result) {
            if (result.autoLogin) {
                console.log('自动登录已启用，将在内容脚本中处理');
            }
        });
    }
});

// 处理来自popup或content script的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('收到消息:', request);
    
    if (request.action === 'getLoginStatus') {
        // 检查登录状态
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const currentTab = tabs[0];
            if (currentTab.url.includes('m.touker.com')) {
                // 在页面中执行脚本检查登录状态
                chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    func: checkLoginStatus
                }, function(results) {
                    if (results && results[0]) {
                        sendResponse({loggedIn: results[0].result});
                    } else {
                        sendResponse({loggedIn: false});
                    }
                });
            } else {
                sendResponse({loggedIn: false});
            }
        });
        return true; // 保持消息通道开放
    }
    
    if (request.action === 'openLoginPage') {
        // 打开登录页面
        chrome.tabs.create({
            url: 'https://m.touker.com/account/login/index.htm?source=stock_trade_h5&referrer=https://m.touker.com/trading/trade/position#/'
        });
        sendResponse({success: true});
    }
});

// 检查登录状态的函数（在页面中执行）
function checkLoginStatus() {
    // 检查是否在交易页面或有用户信息
    if (window.location.href.includes('/trading/') || 
        document.querySelector('.user-info') || 
        document.querySelector('[class*="user-name"]') ||
        document.querySelector('[class*="account"]')) {
        return true;
    }
    
    // 检查是否在登录页面
    if (window.location.href.includes('/account/login')) {
        return false;
    }
    
    // 其他情况，尝试检查页面内容
    const bodyText = document.body.textContent || '';
    if (bodyText.includes('退出') || bodyText.includes('我的账户') || bodyText.includes('持仓')) {
        return true;
    }
    
    return false;
}

// 定期清理存储（可选）
chrome.alarms.create('cleanup', { periodInMinutes: 60 * 24 }); // 每天清理一次

chrome.alarms.onAlarm.addListener(function(alarm) {
    if (alarm.name === 'cleanup') {
        console.log('执行定期清理...');
        // 这里可以添加清理逻辑，比如清理过期的数据
    }
});

// 处理插件图标点击
chrome.action.onClicked.addListener(function(tab) {
    // 如果在华宝证券页面，直接尝试登录
    if (tab.url && tab.url.includes('m.touker.com')) {
        chrome.storage.sync.get(['username', 'password'], function(result) {
            if (result.username && result.password) {
                chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: performQuickLogin,
                    args: [result.username, result.password]
                });
            }
        });
    }
});

// 快速登录函数（在页面中执行）
function performQuickLogin(username, password) {
    console.log('执行快速登录...');
    
    // 这里复用content.js中的登录逻辑
    // 为了避免重复代码，实际实现中可以将登录逻辑提取为共享函数
    
    const usernameInput = document.querySelector('input[type="text"], input[name="username"], input[name="mobile"]');
    const passwordInput = document.querySelector('input[type="password"]');
    const loginButton = document.querySelector('button[type="submit"], .login-btn, button:contains("登录")');
    
    if (usernameInput && passwordInput) {
        usernameInput.value = username;
        passwordInput.value = password;
        
        usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
        passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        if (loginButton) {
            setTimeout(() => loginButton.click(), 500);
        }
    }
}
