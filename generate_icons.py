#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华宝证券插件图标生成器
生成Chrome插件所需的PNG图标文件
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os

def create_simple_icon(size, filename):
    """创建简单的图标"""
    if PIL_AVAILABLE:
        # 使用PIL创建高质量图标
        img = Image.new('RGBA', (size, size), (0, 124, 186, 255))  # 华宝蓝色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制白色圆形
        margin = size // 8
        draw.ellipse([margin, margin, size-margin, size-margin], fill=(255, 255, 255, 255))
        
        # 绘制内部蓝色圆形
        inner_margin = size // 4
        draw.ellipse([inner_margin, inner_margin, size-inner_margin, size-inner_margin], 
                    fill=(0, 124, 186, 255))
        
        # 尝试添加文字（如果字体可用）
        try:
            if size >= 32:
                font_size = size // 4
                try:
                    # 尝试使用系统字体
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    try:
                        font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", font_size)  # 微软雅黑
                    except:
                        font = ImageFont.load_default()
                
                # 计算文字位置
                text = "华"
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                x = (size - text_width) // 2
                y = (size - text_height) // 2
                
                draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        except:
            # 如果添加文字失败，绘制简单的白色方块
            center = size // 2
            rect_size = size // 8
            draw.rectangle([center-rect_size, center-rect_size, center+rect_size, center+rect_size], 
                         fill=(255, 255, 255, 255))
        
        img.save(filename, 'PNG')
        print(f"✅ 已生成 {filename} ({size}x{size})")
        
    else:
        # 如果没有PIL，创建一个简单的SVG然后提示用户
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="{size}" height="{size}" fill="#007CBA"/>
  <circle cx="{size//2}" cy="{size//2}" r="{size//3}" fill="white"/>
  <circle cx="{size//2}" cy="{size//2}" r="{size//6}" fill="#007CBA"/>
  <text x="{size//2}" y="{size//2+size//12}" text-anchor="middle" fill="white" font-size="{size//4}" font-family="Arial">H</text>
</svg>'''
        
        svg_filename = filename.replace('.png', '.svg')
        with open(svg_filename, 'w', encoding='utf-8') as f:
            f.write(svg_content)
        print(f"⚠️  已生成 {svg_filename} (需要手动转换为PNG)")

def main():
    """主函数"""
    print("🎨 华宝证券插件图标生成器")
    print("=" * 40)
    
    # 确保icons目录存在
    icons_dir = "icons"
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
        print(f"📁 已创建 {icons_dir} 目录")
    
    # 生成三种尺寸的图标
    sizes = [16, 48, 128]
    
    if not PIL_AVAILABLE:
        print("⚠️  警告: 未安装PIL/Pillow库")
        print("   请运行: pip install Pillow")
        print("   或者手动转换生成的SVG文件为PNG")
        print()
    
    for size in sizes:
        filename = os.path.join(icons_dir, f"icon{size}.png")
        create_simple_icon(size, filename)
    
    print()
    print("🎉 图标生成完成！")
    
    if not PIL_AVAILABLE:
        print()
        print("📝 后续步骤:")
        print("1. 安装Pillow: pip install Pillow")
        print("2. 重新运行此脚本")
        print("3. 或者使用在线SVG转PNG工具转换生成的SVG文件")
    else:
        print("✅ 所有PNG图标已生成，可以安装插件了！")

if __name__ == "__main__":
    main()
