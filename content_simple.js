// 华宝证券自动登录插件 - 超简化版本
console.log('华宝证券自动登录插件已加载 v1.3-simple');

// 简单的元素查找和填充函数
function fillInput(selector, value, description) {
    console.log(`查找${description}...`);
    
    // 尝试多种方式查找元素
    let element = document.querySelector(selector);
    
    if (!element) {
        console.log(`未找到${description}: ${selector}`);
        return false;
    }
    
    console.log(`找到${description}，开始填充...`);
    
    // 点击并聚焦
    element.click();
    element.focus();
    
    // 设置值
    element.value = value;
    
    // 触发事件
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('keyup', { bubbles: true }));
    
    console.log(`${description}填充完成: ${value}`);
    return true;
}

// 点击元素函数
function clickElement(selector, description) {
    console.log(`查找${description}...`);
    
    let element = document.querySelector(selector);
    
    if (!element) {
        console.log(`未找到${description}: ${selector}`);
        return false;
    }
    
    console.log(`找到${description}，开始点击...`);
    element.click();
    console.log(`${description}点击完成`);
    return true;
}

// 主要的自动登录函数
function performAutoLogin(username, password) {
    console.log('=== 开始华宝证券自动登录 ===');
    
    // 等待页面加载
    setTimeout(() => {
        console.log('第1步：填充用户名');
        
        // 尝试多种用户名输入框选择器
        const usernameSelectors = [
            'input[placeholder*="客户号"]',
            'input[placeholder*="账号"]', 
            'input[placeholder*="用户名"]',
            'input[type="text"]:first-of-type',
            'input[type="text"]'
        ];
        
        let usernameFilled = false;
        for (const selector of usernameSelectors) {
            if (fillInput(selector, username, '用户名输入框')) {
                usernameFilled = true;
                break;
            }
        }
        
        if (!usernameFilled) {
            console.error('❌ 用户名填充失败');
            return;
        }
        
        // 填充密码
        setTimeout(() => {
            console.log('第2步：填充密码');
            
            const passwordSelectors = [
                'input[placeholder*="密码"]',
                'input[type="password"]'
            ];
            
            let passwordFilled = false;
            for (const selector of passwordSelectors) {
                if (fillInput(selector, password, '密码输入框')) {
                    passwordFilled = true;
                    break;
                }
            }
            
            if (!passwordFilled) {
                console.error('❌ 密码填充失败');
                return;
            }
            
            // 勾选同意条款
            setTimeout(() => {
                console.log('第3步：勾选同意条款');
                
                const checkboxSelectors = [
                    'input[type="checkbox"]',
                    'input[type="checkbox"]:not(:checked)'
                ];
                
                for (const selector of checkboxSelectors) {
                    if (clickElement(selector, '同意条款复选框')) {
                        break;
                    }
                }
                
                // 点击登录按钮
                setTimeout(() => {
                    console.log('第4步：点击登录按钮');
                    
                    const loginSelectors = [
                        'button:contains("登录")',
                        'input[value*="登录"]',
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button'
                    ];
                    
                    // 特殊处理包含"登录"文本的按钮
                    const buttons = document.querySelectorAll('button');
                    let loginClicked = false;
                    
                    for (const button of buttons) {
                        if (button.textContent.includes('登录')) {
                            console.log('找到登录按钮，开始点击...');
                            button.click();
                            console.log('登录按钮点击完成');
                            loginClicked = true;
                            break;
                        }
                    }
                    
                    if (!loginClicked) {
                        console.error('❌ 登录按钮点击失败');
                    } else {
                        console.log('✅ 自动登录流程完成');
                    }
                    
                }, 1000);
            }, 1000);
        }, 1000);
    }, 2000);
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'performLogin') {
        performAutoLogin(request.username, request.password);
        sendResponse({success: true});
    }
});

// 检查自动登录设置
chrome.storage.sync.get(['autoLogin', 'username', 'password'], function(result) {
    if (result.autoLogin && result.username && result.password) {
        console.log('检测到自动登录设置，准备自动登录...');
        
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                performAutoLogin(result.username, result.password);
            });
        } else {
            performAutoLogin(result.username, result.password);
        }
    }
});

console.log('华宝证券自动登录插件初始化完成');
