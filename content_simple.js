// 华宝证券自动登录插件 - 超简化版本
console.log('华宝证券自动登录插件已加载 v1.6-enhanced');

// 智能元素查找和填充函数
function fillInput(selectors, value, description) {
    console.log(`查找${description}...`);

    // 如果传入的是字符串，转换为数组
    if (typeof selectors === 'string') {
        selectors = [selectors];
    }

    let element = null;
    let usedSelector = '';

    // 尝试每个选择器
    for (const selector of selectors) {
        try {
            element = document.querySelector(selector);
            if (element) {
                usedSelector = selector;
                console.log(`✅ 通过选择器找到${description}: ${selector}`);
                break;
            }
        } catch (e) {
            console.log(`选择器错误: ${selector}`, e);
        }
    }

    // 如果还没找到，尝试更通用的方法
    if (!element) {
        console.log(`尝试通用方法查找${description}...`);

        if (description.includes('用户名') || description.includes('账号')) {
            // 查找所有text输入框
            const allInputs = document.querySelectorAll('input[type="text"], input:not([type])');
            console.log(`找到 ${allInputs.length} 个文本输入框`);

            for (let i = 0; i < allInputs.length; i++) {
                const input = allInputs[i];
                const placeholder = (input.placeholder || '').toLowerCase();
                const name = (input.name || '').toLowerCase();
                const id = (input.id || '').toLowerCase();

                console.log(`输入框[${i}]: placeholder="${input.placeholder}", name="${input.name}", id="${input.id}"`);

                if (placeholder.includes('客户') || placeholder.includes('账号') ||
                    placeholder.includes('用户') || name.includes('user') ||
                    name.includes('account') || name.includes('cust') ||
                    id.includes('user') || id.includes('account')) {
                    element = input;
                    usedSelector = `通用方法-输入框[${i}]`;
                    console.log(`✅ 通过通用方法找到${description}`);
                    break;
                }
            }

            // 如果还没找到，就用第一个text输入框
            if (!element && allInputs.length > 0) {
                element = allInputs[0];
                usedSelector = '第一个文本输入框';
                console.log(`✅ 使用第一个文本输入框作为${description}`);
            }
        }

        if (description.includes('密码')) {
            // 首先查找password类型的输入框
            let passwordInputs = document.querySelectorAll('input[type="password"]');
            console.log(`找到 ${passwordInputs.length} 个password类型输入框`);

            if (passwordInputs.length === 0) {
                // 如果没有password类型，查找所有输入框
                const allInputs = document.querySelectorAll('input');
                console.log(`查找所有输入框，共 ${allInputs.length} 个`);

                for (let i = 0; i < allInputs.length; i++) {
                    const input = allInputs[i];
                    const placeholder = (input.placeholder || '').toLowerCase();
                    const name = (input.name || '').toLowerCase();
                    const id = (input.id || '').toLowerCase();

                    console.log(`输入框[${i}]: type="${input.type}", placeholder="${input.placeholder}", name="${input.name}"`);

                    if (placeholder.includes('密码') || placeholder.includes('password') ||
                        name.includes('password') || name.includes('pwd') ||
                        id.includes('password') || id.includes('pwd')) {
                        passwordInputs = [input];
                        console.log(`✅ 通过属性匹配找到密码输入框[${i}]`);
                        break;
                    }
                }

                // 如果还是没找到，尝试找第二个输入框（通常密码是第二个）
                if (passwordInputs.length === 0) {
                    const textInputs = document.querySelectorAll('input[type="text"], input:not([type])');
                    if (textInputs.length >= 2) {
                        passwordInputs = [textInputs[1]];
                        console.log(`✅ 使用第二个文本输入框作为密码输入框`);
                    }
                }
            }

            if (passwordInputs.length > 0) {
                element = passwordInputs[0];
                usedSelector = '密码输入框';
                console.log(`✅ 通过通用方法找到${description}`);
            }
        }
    }

    if (!element) {
        console.error(`❌ 未找到${description}`);
        return false;
    }

    console.log(`开始填充${description}，使用: ${usedSelector}`);

    // 点击并聚焦
    element.click();
    element.focus();

    // 设置值
    element.value = value;

    // 触发事件
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('keyup', { bubbles: true }));

    console.log(`✅ ${description}填充完成: ${value}`);
    return true;
}

// 点击元素函数
function clickElement(selector, description) {
    console.log(`查找${description}...`);
    
    let element = document.querySelector(selector);
    
    if (!element) {
        console.log(`未找到${description}: ${selector}`);
        return false;
    }
    
    console.log(`找到${description}，开始点击...`);
    element.click();
    console.log(`${description}点击完成`);
    return true;
}

// 主要的自动登录函数
function performAutoLogin(username, password) {
    console.log('=== 开始华宝证券自动登录 ===');
    
    // 等待页面加载
    setTimeout(() => {
        console.log('第1步：填充用户名');
        
        // 尝试多种用户名输入框选择器
        const usernameSelectors = [
            'input[placeholder*="客户号"]',
            'input[placeholder*="账号"]', 
            'input[placeholder*="用户名"]',
            'input[type="text"]:first-of-type',
            'input[type="text"]'
        ];
        
        let usernameFilled = false;
        for (const selector of usernameSelectors) {
            if (fillInput(selector, username, '用户名输入框')) {
                usernameFilled = true;
                break;
            }
        }
        
        if (!usernameFilled) {
            console.error('❌ 用户名填充失败');
            return;
        }
        
        // 填充密码
        setTimeout(() => {
            console.log('第2步：填充密码');
            
            const passwordSelectors = [
                'input[placeholder*="密码"]',
                'input[type="password"]'
            ];
            
            let passwordFilled = false;
            for (const selector of passwordSelectors) {
                if (fillInput(selector, password, '密码输入框')) {
                    passwordFilled = true;
                    break;
                }
            }
            
            if (!passwordFilled) {
                console.error('❌ 密码填充失败');
                return;
            }
            
            // 勾选同意条款
            setTimeout(() => {
                console.log('第3步：勾选同意条款');
                
                const checkboxSelectors = [
                    'input[type="checkbox"]',
                    'input[type="checkbox"]:not(:checked)'
                ];
                
                for (const selector of checkboxSelectors) {
                    if (clickElement(selector, '同意条款复选框')) {
                        break;
                    }
                }
                
                // 点击登录按钮
                setTimeout(() => {
                    console.log('第4步：点击登录按钮');
                    
                    const loginSelectors = [
                        'button:contains("登录")',
                        'input[value*="登录"]',
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button'
                    ];
                    
                    // 特殊处理包含"登录"文本的按钮
                    const buttons = document.querySelectorAll('button');
                    let loginClicked = false;
                    
                    for (const button of buttons) {
                        if (button.textContent.includes('登录')) {
                            console.log('找到登录按钮，开始点击...');
                            button.click();
                            console.log('登录按钮点击完成');
                            loginClicked = true;
                            break;
                        }
                    }
                    
                    if (!loginClicked) {
                        console.error('❌ 登录按钮点击失败');
                    } else {
                        console.log('✅ 自动登录流程完成');
                    }
                    
                }, 1000);
            }, 1000);
        }, 1000);
    }, 2000);
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'performLogin') {
        performAutoLogin(request.username, request.password);
        sendResponse({success: true});
    }
});

// 检查自动登录设置
chrome.storage.sync.get(['autoLogin', 'username', 'password'], function(result) {
    if (result.autoLogin && result.username && result.password) {
        console.log('检测到自动登录设置，准备自动登录...');
        
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                performAutoLogin(result.username, result.password);
            });
        } else {
            performAutoLogin(result.username, result.password);
        }
    }
});

console.log('华宝证券自动登录插件初始化完成');
