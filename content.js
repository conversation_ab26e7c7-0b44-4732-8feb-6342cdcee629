// 内容脚本 - 在华宝证券登录页面自动执行
console.log('华宝证券自动登录插件已加载');

// 检查是否启用了自动登录
chrome.storage.sync.get(['autoLogin', 'username', 'password'], function(result) {
    if (result.autoLogin && result.username && result.password) {
        console.log('检测到自动登录设置，准备自动登录...');
        
        // 等待页面完全加载后执行登录
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => autoLogin(result.username, result.password), 1000);
            });
        } else {
            setTimeout(() => autoLogin(result.username, result.password), 1000);
        }
    }
});

// 自动登录函数
function autoLogin(username, password) {
    console.log('开始自动登录流程...');
    
    // 检查是否已经登录
    if (window.location.href.includes('/trading/') || 
        document.querySelector('.user-info') || 
        document.querySelector('[class*="user"]')) {
        console.log('用户可能已经登录，跳过自动登录');
        return;
    }

    // 等待元素出现的辅助函数
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // 确保元素可见
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('超时: 未找到元素 ' + selector));
                } else {
                    setTimeout(check, 200);
                }
            }
            
            check();
        });
    }

    // 模拟人工输入
    function simulateTyping(element, text) {
        element.focus();
        
        // 清空现有内容
        element.value = '';
        
        // 逐字符输入
        let index = 0;
        function typeChar() {
            if (index < text.length) {
                element.value += text[index];
                element.dispatchEvent(new Event('input', { bubbles: true }));
                index++;
                setTimeout(typeChar, 50 + Math.random() * 100); // 随机延迟模拟真实输入
            } else {
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.blur();
            }
        }
        
        setTimeout(typeChar, 100);
    }

    // 主要登录逻辑
    async function performAutoLogin() {
        try {
            console.log('查找登录表单元素...');

            // 可能的用户名输入框选择器
            const usernameSelectors = [
                'input[name="username"]',
                'input[name="mobile"]',
                'input[name="phone"]',
                'input[name="account"]',
                'input[type="text"]:not([name="captcha"])',
                'input[placeholder*="用户名"]',
                'input[placeholder*="手机"]',
                'input[placeholder*="账号"]',
                '.username input',
                '.mobile input',
                '.account input'
            ];

            // 可能的密码输入框选择器
            const passwordSelectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[placeholder*="密码"]',
                '.password input'
            ];

            // 查找用户名输入框
            let usernameInput = null;
            for (const selector of usernameSelectors) {
                try {
                    usernameInput = await waitForElement(selector, 3000);
                    console.log('找到用户名输入框:', selector);
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!usernameInput) {
                console.error('未找到用户名输入框');
                return;
            }

            // 查找密码输入框
            let passwordInput = null;
            for (const selector of passwordSelectors) {
                try {
                    passwordInput = await waitForElement(selector, 3000);
                    console.log('找到密码输入框:', selector);
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!passwordInput) {
                console.error('未找到密码输入框');
                return;
            }

            console.log('开始填充登录信息...');

            // 填充用户名
            simulateTyping(usernameInput, username);
            
            // 等待用户名输入完成
            await new Promise(resolve => setTimeout(resolve, username.length * 100 + 500));

            // 填充密码
            simulateTyping(passwordInput, password);
            
            // 等待密码输入完成
            await new Promise(resolve => setTimeout(resolve, password.length * 100 + 500));

            // 查找登录按钮
            const loginButtonSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("登录")',
                'button:contains("登陆")',
                '.login-btn',
                '.submit-btn',
                'button[class*="login"]',
                'button[class*="submit"]',
                'a[class*="login"]'
            ];

            let loginButton = null;
            for (const selector of loginButtonSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent || element.value || '';
                    if (text.includes('登录') || text.includes('登陆') || 
                        element.type === 'submit' || 
                        element.className.toLowerCase().includes('login')) {
                        loginButton = element;
                        break;
                    }
                }
                if (loginButton) break;
            }

            if (loginButton) {
                console.log('找到登录按钮，准备点击...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 模拟点击
                loginButton.focus();
                loginButton.click();
                console.log('已点击登录按钮');
                
                // 监听页面跳转
                setTimeout(() => {
                    if (window.location.href !== location.href) {
                        console.log('登录成功，页面已跳转');
                    }
                }, 2000);
                
            } else {
                // 尝试提交表单
                const form = usernameInput.closest('form');
                if (form) {
                    console.log('未找到登录按钮，尝试提交表单...');
                    form.submit();
                } else {
                    console.error('未找到登录按钮或表单');
                }
            }

        } catch (error) {
            console.error('自动登录过程中出错:', error);
        }
    }

    // 执行自动登录
    performAutoLogin();
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'performLogin') {
        autoLogin(request.username, request.password);
        sendResponse({success: true});
    }
});

// 页面加载完成后的额外检查
window.addEventListener('load', function() {
    console.log('页面加载完成');
    
    // 检查是否有验证码
    const captchaElements = document.querySelectorAll('input[name*="captcha"], input[placeholder*="验证码"], .captcha, .verify-code');
    if (captchaElements.length > 0) {
        console.log('检测到验证码，自动登录可能需要手动处理验证码');
    }
});
