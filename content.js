// 内容脚本 - 在华宝证券登录页面自动执行
console.log('华宝证券自动登录插件已加载');

// 检查是否启用了自动登录
chrome.storage.sync.get(['autoLogin', 'username', 'password'], function(result) {
    if (result.autoLogin && result.username && result.password) {
        console.log('检测到自动登录设置，准备自动登录...');
        
        // 等待页面完全加载后执行登录
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => autoLogin(result.username, result.password), 1000);
            });
        } else {
            setTimeout(() => autoLogin(result.username, result.password), 1000);
        }
    }
});

// 自动登录函数
function autoLogin(username, password) {
    console.log('开始自动登录流程...');
    
    // 检查是否已经登录
    if (window.location.href.includes('/trading/') || 
        document.querySelector('.user-info') || 
        document.querySelector('[class*="user"]')) {
        console.log('用户可能已经登录，跳过自动登录');
        return;
    }

    // 等待元素出现的辅助函数
    function waitForElement(selector, timeout = 15000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // 确保元素可见
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('超时: 未找到元素 ' + selector));
                } else {
                    setTimeout(check, 300);
                }
            }

            check();
        });
    }

    // 通用元素查找函数
    function findElementBySelectors(selectors, description) {
        for (const selector of selectors) {
            try {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    console.log(`找到${description}: ${selector}`);
                    return elements[0];
                }
            } catch (e) {
                console.log(`选择器错误: ${selector}`, e);
            }
        }

        // 如果没找到，尝试更通用的方法
        if (description.includes('用户名') || description.includes('账号')) {
            const allInputs = document.querySelectorAll('input[type="text"]');
            for (const input of allInputs) {
                const placeholder = input.placeholder.toLowerCase();
                const name = input.name.toLowerCase();
                if (placeholder.includes('账号') || placeholder.includes('客户') ||
                    placeholder.includes('用户') || name.includes('user') ||
                    name.includes('account') || name.includes('cust')) {
                    console.log(`通过通用方法找到${description}`);
                    return input;
                }
            }
        }

        if (description.includes('密码')) {
            const passwordInputs = document.querySelectorAll('input[type="password"]');
            if (passwordInputs.length > 0) {
                console.log(`通过通用方法找到${description}`);
                return passwordInputs[0];
            }
        }

        console.log(`未找到${description}`);
        return null;
    }

    // 模拟人工输入
    function simulateTyping(element, text) {
        return new Promise((resolve) => {
            // 先点击激活输入框
            element.click();
            element.focus();

            // 等待输入框激活
            setTimeout(() => {
                // 清空现有内容
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));

                // 逐字符输入
                let index = 0;
                function typeChar() {
                    if (index < text.length) {
                        element.value += text[index];
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('keyup', { bubbles: true }));
                        index++;
                        setTimeout(typeChar, 80 + Math.random() * 120); // 随机延迟模拟真实输入
                    } else {
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        element.dispatchEvent(new Event('blur', { bubbles: true }));
                        resolve();
                    }
                }

                setTimeout(typeChar, 200);
            }, 300);
        });
    }

    // 主要登录逻辑
    async function performAutoLogin() {
        try {
            console.log('查找登录表单元素...');

            // 华宝证券特定的用户名输入框选择器
            const usernameSelectors = [
                'input[placeholder*="华宝证券客户号"]',
                'input[placeholder*="客户号"]',
                'input[placeholder*="账号"]',
                'input[placeholder*="用户名"]',
                'input[placeholder*="手机"]',
                'input[name="username"]',
                'input[name="mobile"]',
                'input[name="phone"]',
                'input[name="account"]',
                'input[name="custNo"]',
                'input[name="customerNo"]',
                'input[type="text"]:not([name="captcha"]):not([placeholder*="验证码"])',
                '.username input',
                '.mobile input',
                '.account input',
                '.cust-no input',
                'input[type="text"]:first-of-type'
            ];

            // 华宝证券特定的密码输入框选择器
            const passwordSelectors = [
                'input[placeholder*="交易密码"]',
                'input[placeholder*="密码"]',
                'input[type="password"]',
                'input[name="password"]',
                'input[name="tradePwd"]',
                'input[name="tradePassword"]',
                '.password input',
                '.trade-pwd input'
            ];

            // 查找用户名输入框
            console.log('查找用户名输入框...');
            let usernameInput = findElementBySelectors(usernameSelectors, '用户名输入框');

            if (!usernameInput) {
                // 等待页面加载完成后再试
                await new Promise(resolve => setTimeout(resolve, 2000));
                usernameInput = findElementBySelectors(usernameSelectors, '用户名输入框');
            }

            if (!usernameInput) {
                console.error('未找到用户名输入框，列出所有text输入框:');
                document.querySelectorAll('input[type="text"]').forEach((el, i) => {
                    console.log(`input[${i}]: placeholder="${el.placeholder}", name="${el.name}", id="${el.id}"`);
                });
                return;
            }

            // 查找密码输入框
            console.log('查找密码输入框...');
            let passwordInput = findElementBySelectors(passwordSelectors, '密码输入框');

            if (!passwordInput) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                passwordInput = findElementBySelectors(passwordSelectors, '密码输入框');
            }

            if (!passwordInput) {
                console.error('未找到密码输入框，列出所有password输入框:');
                document.querySelectorAll('input[type="password"]').forEach((el, i) => {
                    console.log(`password[${i}]: placeholder="${el.placeholder}", name="${el.name}", id="${el.id}"`);
                });
                return;
            }

            console.log('开始填充登录信息...');

            // 填充用户名
            await simulateTyping(usernameInput, username);
            console.log('用户名填充完成');

            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填充密码
            await simulateTyping(passwordInput, password);
            console.log('密码填充完成');

            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 500));

            // 查找并勾选同意条款复选框
            console.log('查找同意条款复选框...');
            const agreementSelectors = [
                'input[type="checkbox"]',
                '.checkbox input',
                'input[name*="agree"]',
                'input[name*="accept"]',
                '[class*="agree"] input',
                '[class*="accept"] input'
            ];

            let agreementCheckbox = null;
            for (const selector of agreementSelectors) {
                try {
                    const checkboxes = document.querySelectorAll(selector);
                    for (const checkbox of checkboxes) {
                        // 查找包含"同意"、"阅读"等关键词的复选框
                        const parentText = checkbox.parentElement ? checkbox.parentElement.textContent : '';
                        const nextText = checkbox.nextElementSibling ? checkbox.nextElementSibling.textContent : '';
                        const combinedText = parentText + nextText;

                        if (combinedText.includes('同意') || combinedText.includes('阅读') ||
                            combinedText.includes('条款') || combinedText.includes('协议') ||
                            combinedText.includes('政策') || combinedText.includes('隐私')) {
                            agreementCheckbox = checkbox;
                            console.log('找到同意条款复选框:', combinedText.trim());
                            break;
                        }
                    }
                    if (agreementCheckbox) break;
                } catch (e) {
                    continue;
                }
            }

            if (agreementCheckbox && !agreementCheckbox.checked) {
                console.log('勾选同意条款复选框...');
                agreementCheckbox.click();
                agreementCheckbox.checked = true;
                agreementCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                await new Promise(resolve => setTimeout(resolve, 300));
            }

            // 查找登录按钮
            const loginButtonSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("登录")',
                'button:contains("登陆")',
                '.login-btn',
                '.submit-btn',
                'button[class*="login"]',
                'button[class*="submit"]',
                'a[class*="login"]'
            ];

            let loginButton = null;
            for (const selector of loginButtonSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent || element.value || '';
                    if (text.includes('登录') || text.includes('登陆') || 
                        element.type === 'submit' || 
                        element.className.toLowerCase().includes('login')) {
                        loginButton = element;
                        break;
                    }
                }
                if (loginButton) break;
            }

            if (loginButton) {
                console.log('找到登录按钮，准备点击...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 模拟点击
                loginButton.focus();
                loginButton.click();
                console.log('已点击登录按钮');
                
                // 监听页面跳转
                setTimeout(() => {
                    if (window.location.href !== location.href) {
                        console.log('登录成功，页面已跳转');
                    }
                }, 2000);
                
            } else {
                // 尝试提交表单
                const form = usernameInput.closest('form');
                if (form) {
                    console.log('未找到登录按钮，尝试提交表单...');
                    form.submit();
                } else {
                    console.error('未找到登录按钮或表单');
                }
            }

        } catch (error) {
            console.error('自动登录过程中出错:', error);
        }
    }

    // 执行自动登录
    performAutoLogin();
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'performLogin') {
        autoLogin(request.username, request.password);
        sendResponse({success: true});
    }
});

// 页面加载完成后的额外检查
window.addEventListener('load', function() {
    console.log('页面加载完成');
    
    // 检查是否有验证码
    const captchaElements = document.querySelectorAll('input[name*="captcha"], input[placeholder*="验证码"], .captcha, .verify-code');
    if (captchaElements.length > 0) {
        console.log('检测到验证码，自动登录可能需要手动处理验证码');
    }
});
