# 华宝证券自动登录插件 - 安装使用指南

## 📋 安装前准备

1. **确保使用Chrome浏览器**（版本88或更高）
2. **准备华宝证券账号信息**（用户名/手机号和密码）

## 🚀 快速安装步骤

### 第一步：生成图标文件
1. 双击打开 `create_icons.html` 文件
2. 浏览器会自动下载三个图标文件
3. 将下载的图标文件移动到 `icons` 文件夹中：
   - `icon16.png` → `icons/icon16.png`
   - `icon48.png` → `icons/icon48.png`
   - `icon128.png` → `icons/icon128.png`

### 第二步：安装插件
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的 **"开发者模式"** 开关
4. 点击 **"加载已解压的扩展程序"**
5. 选择整个 `华宝自动挂单` 文件夹
6. 插件安装成功！

## ⚙️ 首次配置

### 设置登录信息
1. 点击浏览器工具栏中的插件图标 🔵
2. 在弹出窗口中填写：
   - **用户名/手机号**：你的华宝证券账号
   - **密码**：你的登录密码
   - **自动登录**：勾选此项可在访问登录页面时自动登录
3. 点击 **"保存设置"**

## 🎯 使用方法

### 方法一：自动登录（推荐）
1. 确保已勾选"自动登录"选项
2. 访问华宝证券登录页面：
   ```
   https://m.touker.com/account/login/index.htm?source=stock_trade_h5&referrer=https://m.touker.com/trading/trade/position#/
   ```
3. 插件会自动填充并提交登录信息

### 方法二：手动触发登录
1. 在华宝证券登录页面点击插件图标
2. 点击 **"立即登录"** 按钮
3. 插件会自动执行登录流程

### 方法三：快速跳转登录
1. 在任意页面点击插件图标
2. 点击 **"立即登录"**
3. 会自动打开登录页面并执行登录

## 🔧 高级设置

### 修改登录信息
- 随时点击插件图标修改保存的用户名和密码
- 点击"保存设置"更新信息

### 清除数据
- 点击"清除数据"按钮可删除所有保存的登录信息
- 用于更换账号或保护隐私

### 关闭自动登录
- 取消勾选"自动登录"选项
- 仍可使用手动登录功能

## ⚠️ 注意事项

### 安全提醒
- ✅ 登录信息仅保存在本地浏览器中
- ✅ 不会上传到任何服务器
- ⚠️ 不要在公共电脑上保存密码
- ⚠️ 建议定期更改密码

### 使用限制
- 🔸 仅支持华宝证券手机版网站
- 🔸 如遇验证码需手动输入
- 🔸 网络异常时可能登录失败

## 🐛 故障排除

### 插件无法安装
**问题**：提示"无法加载扩展程序"
**解决**：
1. 确认已开启开发者模式
2. 检查文件夹结构是否完整
3. 确保 `manifest.json` 文件存在

### 登录失败
**问题**：点击登录后没有反应
**解决**：
1. 检查用户名密码是否正确
2. 刷新登录页面重试
3. 查看浏览器控制台错误信息（F12）

### 自动登录不工作
**问题**：访问登录页面时不自动登录
**解决**：
1. 确认已勾选"自动登录"
2. 确认已保存正确的登录信息
3. 清除浏览器缓存重试

### 找不到登录按钮
**问题**：提示"未找到登录按钮"
**解决**：
1. 华宝证券可能更新了页面结构
2. 手动点击登录按钮
3. 联系开发者更新插件

## 📱 支持的页面格式

插件会自动识别以下类型的登录表单：
- 标准用户名/密码表单
- 手机号/密码表单
- 账号/密码表单
- 各种CSS样式的登录按钮

## 🔄 更新插件

当有新版本时：
1. 下载新的插件文件
2. 在扩展程序页面点击"重新加载"
3. 或删除旧版本重新安装

## 📞 技术支持

### 常见问题
- 查看本文档的故障排除部分
- 检查浏览器控制台错误信息

### 反馈问题
如遇到问题，请提供以下信息：
- Chrome浏览器版本
- 插件版本
- 错误截图或描述
- 浏览器控制台错误信息

## 📄 文件清单

确保以下文件都存在：
```
华宝自动挂单/
├── manifest.json          ✅ 插件配置
├── popup.html             ✅ 设置界面
├── popup.js               ✅ 界面逻辑
├── content.js             ✅ 页面脚本
├── background.js          ✅ 后台服务
├── create_icons.html      ✅ 图标生成器
├── README.md              ✅ 详细说明
├── 安装指南.md            ✅ 本文件
└── icons/                 ✅ 图标文件夹
    ├── icon16.png         ⚠️ 需要生成
    ├── icon48.png         ⚠️ 需要生成
    └── icon128.png        ⚠️ 需要生成
```

---

**祝你使用愉快！** 🎉

如有任何问题，请仔细阅读本指南或查看 README.md 文件。
