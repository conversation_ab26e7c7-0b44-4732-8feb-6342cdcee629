// 弹出窗口脚本
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const autoLoginCheckbox = document.getElementById('autoLogin');
    const saveBtn = document.getElementById('saveBtn');
    const loginBtn = document.getElementById('loginBtn');
    const clearBtn = document.getElementById('clearBtn');
    const statusDiv = document.getElementById('status');

    // 加载保存的设置
    loadSettings();

    // 保存设置
    saveBtn.addEventListener('click', function() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        const autoLogin = autoLoginCheckbox.checked;

        if (!username || !password) {
            showStatus('请输入用户名和密码', 'error');
            return;
        }

        // 保存到Chrome存储
        chrome.storage.sync.set({
            username: username,
            password: password,
            autoLogin: autoLogin
        }, function() {
            showStatus('设置已保存', 'success');
        });
    });

    // 立即登录
    loginBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const currentTab = tabs[0];
            
            // 检查是否在登录页面
            if (currentTab.url.includes('m.touker.com/account/login')) {
                // 在当前页面执行登录
                executeLogin(currentTab.id);
            } else {
                // 打开登录页面
                chrome.tabs.create({
                    url: 'https://m.touker.com/account/login/index.htm?source=stock_trade_h5&referrer=https://m.touker.com/trading/trade/position#/'
                }, function(newTab) {
                    // 等待页面加载后执行登录
                    setTimeout(() => {
                        executeLogin(newTab.id);
                    }, 2000);
                });
            }
        });
    });

    // 清除数据
    clearBtn.addEventListener('click', function() {
        chrome.storage.sync.clear(function() {
            usernameInput.value = '';
            passwordInput.value = '';
            autoLoginCheckbox.checked = false;
            showStatus('数据已清除', 'success');
        });
    });

    // 加载设置
    function loadSettings() {
        chrome.storage.sync.get(['username', 'password', 'autoLogin'], function(result) {
            if (result.username) {
                usernameInput.value = result.username;
            }
            if (result.password) {
                passwordInput.value = result.password;
            }
            autoLoginCheckbox.checked = result.autoLogin || false;
        });
    }

    // 执行登录
    function executeLogin(tabId) {
        chrome.storage.sync.get(['username', 'password'], function(result) {
            if (!result.username || !result.password) {
                showStatus('请先保存用户名和密码', 'error');
                return;
            }

            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: performLogin,
                args: [result.username, result.password]
            }, function(results) {
                if (chrome.runtime.lastError) {
                    showStatus('登录失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    showStatus('正在尝试登录...', 'success');
                }
            });
        });
    }

    // 显示状态信息
    function showStatus(message, type) {
        statusDiv.textContent = message;
        statusDiv.className = 'status ' + type;
        statusDiv.style.display = 'block';
        
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
});

// 在页面中执行的登录函数
function performLogin(username, password) {
    console.log('开始自动登录...');
    
    // 等待页面完全加载
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('元素未找到: ' + selector));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }

    // 尝试多种可能的选择器
    const usernameSelectors = [
        'input[placeholder*="账号"]',
        'input[placeholder*="用户名"]',
        'input[placeholder*="手机"]',
        'input[type="text"]:not([placeholder*="验证码"])',
        'input[name="username"]',
        'input[name="mobile"]',
        'input[name="phone"]',
        'input[type="text"]:first-of-type'
    ];

    const passwordSelectors = [
        'input[placeholder*="密码"]',
        'input[type="password"]',
        'input[name="password"]'
    ];

    const loginButtonSelectors = [
        'button:contains("登录")',
        'button:contains("登陆")',
        'input[value*="登录"]',
        'input[value*="登陆"]',
        'button[type="submit"]',
        'input[type="submit"]',
        '.login-btn',
        '.submit-btn',
        '[class*="login"]',
        '[class*="submit"]',
        'button',
        'input[type="button"]'
    ];

    async function fillAndSubmit() {
        try {
            // 查找用户名输入框
            let usernameInput = null;
            for (const selector of usernameSelectors) {
                try {
                    usernameInput = await waitForElement(selector, 2000);
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!usernameInput) {
                throw new Error('未找到用户名输入框');
            }

            // 查找密码输入框
            let passwordInput = null;
            for (const selector of passwordSelectors) {
                try {
                    passwordInput = await waitForElement(selector, 2000);
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!passwordInput) {
                throw new Error('未找到密码输入框');
            }

            // 填充用户名 - 先点击激活
            usernameInput.click();
            usernameInput.focus();
            await new Promise(resolve => setTimeout(resolve, 300));

            usernameInput.value = username;
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('keyup', { bubbles: true }));

            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 800));

            // 填充密码 - 先点击激活
            passwordInput.click();
            passwordInput.focus();
            await new Promise(resolve => setTimeout(resolve, 300));

            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('keyup', { bubbles: true }));

            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 800));

            // 查找并勾选同意条款复选框
            console.log('查找同意条款复选框...');
            const agreementCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of agreementCheckboxes) {
                const parentText = checkbox.parentElement ? checkbox.parentElement.textContent : '';
                const nextText = checkbox.nextElementSibling ? checkbox.nextElementSibling.textContent : '';
                const combinedText = parentText + nextText;

                if (combinedText.includes('同意') || combinedText.includes('阅读') ||
                    combinedText.includes('条款') || combinedText.includes('协议') ||
                    combinedText.includes('政策') || combinedText.includes('隐私')) {
                    if (!checkbox.checked) {
                        console.log('勾选同意条款复选框');
                        checkbox.click();
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                    break;
                }
            }

            // 查找并点击登录按钮
            let loginButton = null;
            for (const selector of loginButtonSelectors) {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    if (button.textContent.includes('登录') || button.textContent.includes('登陆') || 
                        button.type === 'submit' || button.className.includes('login')) {
                        loginButton = button;
                        break;
                    }
                }
                if (loginButton) break;
            }

            if (!loginButton) {
                // 如果没找到按钮，尝试提交表单
                const form = usernameInput.closest('form');
                if (form) {
                    form.submit();
                    console.log('通过表单提交登录');
                } else {
                    throw new Error('未找到登录按钮或表单');
                }
            } else {
                loginButton.click();
                console.log('点击登录按钮');
            }

            console.log('登录请求已发送');
            
        } catch (error) {
            console.error('自动登录失败:', error);
            alert('自动登录失败: ' + error.message);
        }
    }

    fillAndSubmit();
}
