<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>华宝证券插件图标生成器</h1>
    
    <div class="icon-container">
        <h3>16x16 图标</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <button onclick="downloadIcon('icon16', 16)">下载 16x16</button>
    </div>
    
    <div class="icon-container">
        <h3>48x48 图标</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
        <button onclick="downloadIcon('icon48', 48)">下载 48x48</button>
    </div>
    
    <div class="icon-container">
        <h3>128x128 图标</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
        <button onclick="downloadIcon('icon128', 128)">下载 128x128</button>
    </div>

    <script>
        function drawIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#007cba');
            gradient.addColorStop(1, '#005a87');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制华宝证券标志（简化版）
            ctx.fillStyle = 'white';
            
            if (size >= 48) {
                // 大图标 - 绘制详细图标
                const centerX = size / 2;
                const centerY = size / 2;
                const radius = size * 0.3;
                
                // 绘制圆形
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.fill();
                
                // 绘制内部图案
                ctx.fillStyle = '#007cba';
                ctx.font = `${size * 0.25}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('华', centerX, centerY);
                
                // 绘制边框装饰
                ctx.strokeStyle = 'white';
                ctx.lineWidth = size * 0.05;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius * 1.2, 0, 2 * Math.PI);
                ctx.stroke();
                
            } else {
                // 小图标 - 简化设计
                const centerX = size / 2;
                const centerY = size / 2;
                const radius = size * 0.35;
                
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.fill();
                
                // 简单的内部标记
                ctx.fillStyle = '#007cba';
                ctx.fillRect(centerX - 2, centerY - 2, 4, 4);
            }
        }
        
        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 生成所有图标
        drawIcon('icon16', 16);
        drawIcon('icon48', 48);
        drawIcon('icon128', 128);
        
        // 页面加载完成后提示用户
        window.onload = function() {
            alert('页面加载完成！请点击下载按钮获取图标文件，然后将它们保存到 icons 文件夹中。');
        };
    </script>
</body>
</html>
